"""
WebSocket处理模块
"""
import json
from datetime import datetime
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room
from flask_login import current_user
from enum import Enum


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    SUCCESS = "success"


class LogNode:
    """日志节点类"""
    
    def __init__(self, node_id, name, description="", parent_id=None):
        self.node_id = node_id
        self.name = name
        self.description = description
        self.parent_id = parent_id
        self.status = "pending"  # pending, running, completed, failed
        self.progress = 0
        self.start_time = None
        self.end_time = None
        self.logs = []
        self.children = []
        self.metadata = {}
    
    def start(self):
        """开始节点执行"""
        self.status = "running"
        self.start_time = datetime.utcnow()
        self.progress = 0
    
    def complete(self, progress=100):
        """完成节点执行"""
        self.status = "completed"
        self.end_time = datetime.utcnow()
        self.progress = progress
    
    def fail(self, error_message=""):
        """节点执行失败"""
        self.status = "failed"
        self.end_time = datetime.utcnow()
        if error_message:
            self.add_log(LogLevel.ERROR, error_message)
    
    def update_progress(self, progress):
        """更新进度"""
        self.progress = min(100, max(0, progress))
    
    def add_log(self, level, message, metadata=None):
        """添加日志"""
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': level.value if isinstance(level, LogLevel) else level,
            'message': message,
            'metadata': metadata or {}
        }
        self.logs.append(log_entry)
        return log_entry
    
    def add_child(self, child_node):
        """添加子节点"""
        child_node.parent_id = self.node_id
        self.children.append(child_node)
        return child_node
    
    def to_dict(self):
        """转换为字典"""
        return {
            'node_id': self.node_id,
            'name': self.name,
            'description': self.description,
            'parent_id': self.parent_id,
            'status': self.status,
            'progress': self.progress,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'logs': self.logs,
            'children': [child.to_dict() for child in self.children],
            'metadata': self.metadata
        }


class TaskLogger:
    """任务日志管理器"""
    
    def __init__(self, task_id, socketio=None):
        self.task_id = task_id
        self.socketio = socketio
        self.root_node = LogNode("root", "搜索任务", f"任务ID: {task_id}")
        self.current_node = self.root_node
        self.nodes = {"root": self.root_node}
    
    def create_node(self, node_id, name, description="", parent_id=None):
        """创建新节点"""
        if parent_id and parent_id in self.nodes:
            parent_node = self.nodes[parent_id]
        else:
            parent_node = self.current_node
        
        node = LogNode(node_id, name, description, parent_node.node_id)
        parent_node.add_child(node)
        self.nodes[node_id] = node
        
        # 发送节点创建事件
        self._emit_event('node_created', {
            'task_id': self.task_id,
            'node': node.to_dict()
        })
        
        return node
    
    def start_node(self, node_id):
        """开始节点"""
        if node_id in self.nodes:
            node = self.nodes[node_id]
            node.start()
            self.current_node = node
            
            # 发送节点开始事件
            self._emit_event('node_started', {
                'task_id': self.task_id,
                'node_id': node_id,
                'node': node.to_dict()
            })
    
    def complete_node(self, node_id, progress=100):
        """完成节点"""
        if node_id in self.nodes:
            node = self.nodes[node_id]
            node.complete(progress)
            
            # 发送节点完成事件
            self._emit_event('node_completed', {
                'task_id': self.task_id,
                'node_id': node_id,
                'node': node.to_dict()
            })
    
    def fail_node(self, node_id, error_message=""):
        """节点失败"""
        if node_id in self.nodes:
            node = self.nodes[node_id]
            node.fail(error_message)
            
            # 发送节点失败事件
            self._emit_event('node_failed', {
                'task_id': self.task_id,
                'node_id': node_id,
                'node': node.to_dict(),
                'error': error_message
            })
    
    def update_progress(self, node_id, progress):
        """更新节点进度"""
        if node_id in self.nodes:
            node = self.nodes[node_id]
            node.update_progress(progress)
            
            # 发送进度更新事件
            self._emit_event('progress_updated', {
                'task_id': self.task_id,
                'node_id': node_id,
                'progress': progress
            })
    
    def log(self, level, message, node_id=None, metadata=None):
        """添加日志"""
        if node_id and node_id in self.nodes:
            node = self.nodes[node_id]
        else:
            node = self.current_node
        
        log_entry = node.add_log(level, message, metadata)
        
        # 发送日志事件
        self._emit_event('log_added', {
            'task_id': self.task_id,
            'node_id': node.node_id,
            'log': log_entry
        })
        
        return log_entry
    
    def get_task_tree(self):
        """获取完整的任务树"""
        return self.root_node.to_dict()
    
    def _emit_event(self, event, data):
        """发送WebSocket事件"""
        if self.socketio:
            self.socketio.emit(event, data, room=f"task_{self.task_id}")


# 全局SocketIO实例
socketio = None

def init_socketio(app):
    """初始化SocketIO"""
    global socketio
    socketio = SocketIO(app, cors_allowed_origins="*")
    
    @socketio.on('connect')
    def handle_connect():
        if current_user.is_authenticated:
            print(f'用户 {current_user.username} 连接到WebSocket')
        else:
            print('匿名用户连接到WebSocket')
    
    @socketio.on('disconnect')
    def handle_disconnect():
        if current_user.is_authenticated:
            print(f'用户 {current_user.username} 断开WebSocket连接')
        else:
            print('匿名用户断开WebSocket连接')
    
    @socketio.on('join_task')
    def handle_join_task(data):
        """加入任务房间"""
        task_id = data.get('task_id')
        if task_id and current_user.is_authenticated:
            join_room(f"task_{task_id}")
            emit('joined_task', {'task_id': task_id})
            print(f'用户 {current_user.username} 加入任务 {task_id} 房间')
    
    @socketio.on('leave_task')
    def handle_leave_task(data):
        """离开任务房间"""
        task_id = data.get('task_id')
        if task_id and current_user.is_authenticated:
            leave_room(f"task_{task_id}")
            emit('left_task', {'task_id': task_id})
            print(f'用户 {current_user.username} 离开任务 {task_id} 房间')
    
    return socketio


def get_socketio():
    """获取SocketIO实例"""
    return socketio


def create_task_logger(task_id):
    """创建任务日志管理器"""
    return TaskLogger(task_id, socketio)
