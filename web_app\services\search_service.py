"""
搜索服务 - 集成现有的搜索调研功能
"""
import os
import sys
import threading
import uuid
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from src.core.search_research import SearchResearchClass
from web_app.models.ai_source import AISource
from web_app.models.data_source import DataSource
from web_app.utils.websocket import create_task_logger, LogLevel


class WebSearchResearchWrapper:
    """Web搜索调研包装器，集成实时日志功能"""

    def __init__(self, task_logger):
        self.task_logger = task_logger
        self.researcher = SearchResearchClass()

    def research_company(self, company_name: str):
        """执行公司调研，带实时日志"""
        try:
            # 步骤1: 搜索公司官网
            self.task_logger.log(LogLevel.INFO, f"🔍 [搜索官网] 任务开始")
            self.task_logger.log(LogLevel.INFO, f"🔍 [搜索官网] 搜索中 - 查找 {company_name} 的官方网站")

            # 调用原始方法的内部逻辑
            cleaned_company_name = company_name.strip()
            base_url = self.researcher._search_official_website(cleaned_company_name)

            if base_url:
                self.task_logger.log(LogLevel.SUCCESS, f"🔍 [搜索官网] 任务成功，结果：找到官网 {base_url}")
            else:
                self.task_logger.log(LogLevel.ERROR, f"🔍 [搜索官网] 任务失败，错误：无法找到公司官网")
                return {
                    "company_name": cleaned_company_name,
                    "status": "failed",
                    "error": "无法找到公司官网"
                }
            self.task_logger.log(LogLevel.INFO, f"🔍 [搜索官网] 任务执行完毕")

            # 步骤2: 分析新闻板块
            self.task_logger.log(LogLevel.INFO, f"📰 [分析新闻板块] 任务开始")
            self.task_logger.log(LogLevel.INFO, f"📰 [分析新闻板块] 分析中 - 从官网首页提取新闻板块信息")

            news_sections_result = self.researcher._extract_news_sections(cleaned_company_name, base_url)

            if news_sections_result:
                investor_count = len(news_sections_result.get("investor_relations_urls", []))
                news_count = len(news_sections_result.get("news_section_urls", []))
                mixed_count = len(news_sections_result.get("mixed_section_urls", []))

                self.task_logger.log(LogLevel.SUCCESS,
                    f"📰 [分析新闻板块] 任务成功，结果：投资者关系 {investor_count} 个，新闻板块 {news_count} 个，混合板块 {mixed_count} 个")
            else:
                self.task_logger.log(LogLevel.WARNING, f"📰 [分析新闻板块] 新方法失败，回退到原有方法")
                # 回退逻辑
                investor_urls = self.researcher._extract_investor_relations_urls(cleaned_company_name, base_url)
                news_sections_result = {
                    "investor_relations_urls": investor_urls,
                    "news_section_urls": [],
                    "mixed_section_urls": []
                }
                self.task_logger.log(LogLevel.INFO, f"📰 [分析新闻板块] 回退成功，找到投资者关系页面 {len(investor_urls)} 个")

            self.task_logger.log(LogLevel.INFO, f"📰 [分析新闻板块] 任务执行完毕")

            # 步骤3: 提取XPath规则
            self.task_logger.log(LogLevel.INFO, f"🧩 [提取XPath规则] 任务开始")

            # 收集所有需要分析的页面
            all_pages_to_analyze = []
            for url in news_sections_result.get("investor_relations_urls", []):
                all_pages_to_analyze.append((url, "investor_relations"))
            for url in news_sections_result.get("news_section_urls", []):
                all_pages_to_analyze.append((url, "news_section"))
            for url in news_sections_result.get("mixed_section_urls", []):
                all_pages_to_analyze.append((url, "mixed_section"))

            self.task_logger.log(LogLevel.INFO, f"🧩 [提取XPath规则] 分析中 - 处理 {len(all_pages_to_analyze)} 个页面")

            xpath_results = self.researcher._extract_classified_xpath_rules(cleaned_company_name, all_pages_to_analyze)

            if xpath_results:
                rule_count = xpath_results.get("statistics", {}).get("total_rules", 0)
                url_count = len(xpath_results.get("url_xpath_mapping", {}))
                self.task_logger.log(LogLevel.SUCCESS,
                    f"🧩 [提取XPath规则] 任务成功，结果：生成 {rule_count} 条规则，覆盖 {url_count} 个URL")
            else:
                self.task_logger.log(LogLevel.WARNING, f"🧩 [提取XPath规则] 未能提取到XPath规则")
                xpath_results = {}

            self.task_logger.log(LogLevel.INFO, f"🧩 [提取XPath规则] 任务执行完毕")

            # 组装最终结果
            result = {
                "company_name": cleaned_company_name,
                "base_url": base_url,
                "investor_relations_urls": news_sections_result.get("investor_relations_urls", []),
                "news_section_urls": news_sections_result.get("news_section_urls", []),
                "mixed_section_urls": news_sections_result.get("mixed_section_urls", []),
                "url_xpath_mapping": xpath_results.get("url_xpath_mapping", {}),
                "xpath_statistics": xpath_results.get("statistics", {}),
                "research_timestamp": __import__('time').time(),
                "status": "completed"
            }

            # 添加传统字段以保持兼容性
            if "xpath_result_object" in xpath_results:
                xpath_obj = xpath_results["xpath_result_object"]
                result["xpath_rules_with_sources"] = xpath_obj.get_all_rules_with_sources()

                # 提取分类规则
                result["investor_xpath_rules"] = xpath_obj.get_investor_rules()
                result["news_xpath_rules"] = xpath_obj.get_news_rules()
                result["general_xpath_rules"] = xpath_obj.get_general_rules()
                result["all_xpath_rules"] = xpath_obj.get_all_rules()

            return result

        except Exception as e:
            error_msg = str(e)
            self.task_logger.log(LogLevel.ERROR, f"❌ [调研任务] 任务失败，错误：{error_msg}")
            return {
                "company_name": company_name,
                "status": "failed",
                "error": error_msg,
                "research_timestamp": __import__('time').time()
            }


class WebSearchService:
    """Web搜索服务类"""
    
    def __init__(self):
        self.active_tasks = {}
        self.task_results = {}
    
    def start_search_task(self, user_id, ai_source_id, data_source_id, company_name, options=None):
        """
        启动搜索任务
        
        Args:
            user_id: 用户ID
            ai_source_id: AI源ID
            data_source_id: 数据源ID
            company_name: 公司名称
            options: 搜索选项
            
        Returns:
            tuple: (success, message, task_id)
        """
        try:
            # 验证AI源和数据源
            ai_source = AISource.query.filter_by(id=ai_source_id, enabled=True).first()
            if not ai_source:
                return False, "AI源不存在或已禁用", None
            
            data_source = DataSource.query.filter_by(id=data_source_id, enabled=True).first()
            if not data_source:
                return False, "数据源不存在或已禁用", None
            
            # 生成任务ID
            task_id = str(uuid.uuid4())
            
            # 创建任务信息
            task_info = {
                'task_id': task_id,
                'user_id': user_id,
                'ai_source': ai_source.to_dict(),
                'data_source': data_source.to_dict(),
                'company_name': company_name,
                'options': options or {},
                'status': 'started',
                'created_at': datetime.utcnow(),
                'started_at': None,
                'completed_at': None,
                'result': None,
                'error': None
            }
            
            self.active_tasks[task_id] = task_info
            
            # 启动后台搜索线程
            search_thread = threading.Thread(
                target=self._execute_search,
                args=(task_id, ai_source, data_source, company_name, options)
            )
            search_thread.daemon = True
            search_thread.start()
            
            return True, "搜索任务已启动", task_id
            
        except Exception as e:
            return False, f"启动搜索任务失败: {str(e)}", None
    
    def _execute_search(self, task_id, ai_source, data_source, company_name, options):
        """
        执行搜索任务（后台线程）
        
        Args:
            task_id: 任务ID
            ai_source: AI源对象
            data_source: 数据源对象
            company_name: 公司名称
            options: 搜索选项
        """
        task_logger = create_task_logger(task_id)
        
        try:
            # 更新任务状态
            if task_id in self.active_tasks:
                self.active_tasks[task_id]['status'] = 'running'
                self.active_tasks[task_id]['started_at'] = datetime.utcnow()
            
            # 创建搜索节点结构
            task_logger.start_node("root")
            task_logger.log(LogLevel.INFO, f"🚀 开始搜索任务: {company_name}")
            task_logger.log(LogLevel.INFO, f"📋 任务ID: {task_id}")
            task_logger.log(LogLevel.INFO, f"👤 用户ID: {self.active_tasks.get(task_id, {}).get('user_id', 'unknown')}")

            # 初始化节点
            init_node = task_logger.create_node("init", "🔧 系统初始化", "配置搜索环境和参数")
            task_logger.start_node("init")
            task_logger.log(LogLevel.INFO, f"🤖 AI源: {ai_source.name} ({ai_source.model_name})")
            task_logger.log(LogLevel.INFO, f"🔍 数据源: {data_source.name} ({data_source.source_type})")
            task_logger.log(LogLevel.INFO, f"⚙️ 搜索选项: {options}")

            # 配置环境变量（从AI源和数据源配置中获取）
            self._configure_environment(ai_source, data_source, task_logger)
            task_logger.complete_node("init")

            # 创建搜索研究实例
            search_node = task_logger.create_node("search", "🔍 执行搜索调研", "使用WebSearchResearchWrapper进行公司调研")
            task_logger.start_node("search")

            try:
                # 实例化搜索研究包装器
                task_logger.log(LogLevel.INFO, "📦 [初始化] 任务开始")
                task_logger.log(LogLevel.INFO, "📦 [初始化] 初始化中 - 创建搜索研究引擎")
                researcher = WebSearchResearchWrapper(task_logger)
                task_logger.log(LogLevel.SUCCESS, "📦 [初始化] 任务成功，结果：搜索引擎初始化完成")
                task_logger.log(LogLevel.INFO, "📦 [初始化] 任务执行完毕")

                # 执行搜索（使用包装器，它会提供详细的实时日志）
                result = researcher.research_company(company_name)

                task_logger.log(LogLevel.SUCCESS, "🎉 搜索调研完成")
                task_logger.complete_node("search")

                # 处理结果
                result_node = task_logger.create_node("result", "📋 处理结果", "清理和格式化搜索结果")
                task_logger.start_node("result")

                task_logger.log(LogLevel.INFO, "🧹 正在清理和格式化结果...")

                # 记录原始结果统计
                if result:
                    task_logger.log(LogLevel.INFO, f"🏢 发现官网: {result.get('base_url', 'N/A')}")
                    task_logger.log(LogLevel.INFO, f"💼 投资者关系页面: {len(result.get('investor_relations_urls', []))} 个")
                    task_logger.log(LogLevel.INFO, f"📰 新闻板块页面: {len(result.get('news_section_urls', []))} 个")
                    task_logger.log(LogLevel.INFO, f"🔀 混合板块页面: {len(result.get('mixed_section_urls', []))} 个")

                    # 统计XPath规则
                    xpath_count = 0
                    for field in ['news_xpath_rules', 'investor_xpath_rules', 'general_xpath_rules']:
                        if field in result and isinstance(result[field], list):
                            count = len(result[field])
                            xpath_count += count
                            task_logger.log(LogLevel.INFO, f"🧩 {field.replace('_', ' ').title()}: {count} 条")

                    task_logger.log(LogLevel.INFO, f"📊 总计XPath规则: {xpath_count} 条")

                # 清理结果
                cleaned_result = self._clean_result(result)

                # 记录清理后的统计
                if cleaned_result:
                    stats = cleaned_result.get('analysis_summary', {})
                    task_logger.log(LogLevel.SUCCESS, f"✨ 结果处理完成")
                    task_logger.log(LogLevel.INFO, f"📈 总计发现板块: {stats.get('total_sections_found', 0)} 个")
                    task_logger.log(LogLevel.INFO, f"🎯 置信度: {(cleaned_result.get('confidence', 0) * 100):.1f}%")
                    task_logger.log(LogLevel.INFO, f"🔗 唯一URL: {stats.get('unique_urls', 0)} 个")

                task_logger.complete_node("result")
                task_logger.complete_node("root")
                
                # 保存结果
                if task_id in self.active_tasks:
                    self.active_tasks[task_id]['status'] = 'completed'
                    self.active_tasks[task_id]['completed_at'] = datetime.utcnow()
                    self.active_tasks[task_id]['result'] = cleaned_result
                
                self.task_results[task_id] = cleaned_result
                task_logger.log(LogLevel.SUCCESS, "任务执行成功完成")
                
            except Exception as search_error:
                task_logger.fail_node("search", f"搜索执行失败: {str(search_error)}")
                raise search_error
                
        except Exception as e:
            error_message = f"任务执行失败: {str(e)}"
            task_logger.log(LogLevel.ERROR, error_message)
            task_logger.fail_node("root", error_message)
            
            # 更新任务状态
            if task_id in self.active_tasks:
                self.active_tasks[task_id]['status'] = 'failed'
                self.active_tasks[task_id]['completed_at'] = datetime.utcnow()
                self.active_tasks[task_id]['error'] = error_message
    
    def _configure_environment(self, ai_source, data_source, task_logger):
        """配置环境变量"""
        try:
            # 配置AI源相关环境变量
            ai_config = ai_source.get_config_params()
            if ai_source.api_key:
                os.environ['OPENAI_API_KEY'] = ai_source.api_key
                task_logger.log(LogLevel.INFO, "已配置OpenAI API密钥")
            
            if ai_source.endpoint_url:
                os.environ['OPENAI_BASE_URL'] = ai_source.endpoint_url
                task_logger.log(LogLevel.INFO, f"已配置OpenAI端点: {ai_source.endpoint_url}")
            
            if ai_source.model_name:
                os.environ['OPENAI_MODEL'] = ai_source.model_name
                task_logger.log(LogLevel.INFO, f"已配置模型: {ai_source.model_name}")
            
            # 配置数据源相关环境变量
            data_config = data_source.get_config_params()
            if data_source.source_type == 'google_search':
                if data_config.get('api_url'):
                    os.environ['GOOGLE_SEARCH_API_URL'] = data_config['api_url']
                    task_logger.log(LogLevel.INFO, "已配置Google搜索API")
            
            task_logger.log(LogLevel.SUCCESS, "环境配置完成")
            
        except Exception as e:
            task_logger.log(LogLevel.WARNING, f"环境配置警告: {str(e)}")
    
    def _clean_result(self, result):
        """清理和格式化搜索结果，保留重要字段并添加统计信息"""
        if not result:
            return {}

        # 保留原始结果的副本
        cleaned_result = result.copy()

        # 计算统计信息
        stats = self._calculate_statistics(result)
        cleaned_result['analysis_summary'] = stats

        # 格式化置信度信息
        if 'xpath_statistics' in result and 'avg_confidence' in result['xpath_statistics']:
            avg_conf = result['xpath_statistics']['avg_confidence']
            if isinstance(avg_conf, dict):
                # 计算总体平均置信度
                confidences = [v for v in avg_conf.values() if isinstance(v, (int, float))]
                if confidences:
                    cleaned_result['confidence'] = sum(confidences) / len(confidences)
                else:
                    cleaned_result['confidence'] = 0.0
            else:
                cleaned_result['confidence'] = avg_conf if isinstance(avg_conf, (int, float)) else 0.0
        else:
            cleaned_result['confidence'] = 0.0

        # 确保官网地址字段存在
        if 'base_url' in result and not cleaned_result.get('official_website'):
            cleaned_result['official_website'] = result['base_url']

        # 保留xpath规则用于展示，但进行分类整理
        cleaned_result['xpath_rules_summary'] = self._organize_xpath_rules(result)

        # 移除内部使用的字段（根据用户偏好）
        fields_to_remove = ['_xpath_result_object']
        for field in fields_to_remove:
            cleaned_result.pop(field, None)

        return cleaned_result

    def _calculate_statistics(self, result):
        """计算分析统计信息"""
        stats = {
            'total_sections_found': 0,
            'investor_sections': 0,
            'news_sections': 0,
            'mixed_sections': 0,
            'xpath_rules_generated': 0,
            'unique_urls': 0
        }

        # 统计URL数量
        investor_urls = result.get('investor_relations_urls', [])
        news_urls = result.get('news_section_urls', [])
        mixed_urls = result.get('mixed_section_urls', [])

        stats['investor_sections'] = len(investor_urls)
        stats['news_sections'] = len(news_urls)
        stats['mixed_sections'] = len(mixed_urls)
        stats['total_sections_found'] = stats['investor_sections'] + stats['news_sections'] + stats['mixed_sections']

        # 统计唯一URL数量
        all_urls = set(investor_urls + news_urls + mixed_urls)
        stats['unique_urls'] = len(all_urls)

        # 统计XPath规则数量
        if 'xpath_statistics' in result:
            xpath_stats = result['xpath_statistics']
            stats['xpath_rules_generated'] = xpath_stats.get('total_rules', 0)
        else:
            # 手动计算
            xpath_count = 0
            for field in ['news_xpath_rules', 'investor_xpath_rules', 'general_xpath_rules']:
                if field in result and isinstance(result[field], list):
                    xpath_count += len(result[field])
            stats['xpath_rules_generated'] = xpath_count

        return stats

    def _organize_xpath_rules(self, result):
        """整理和分类XPath规则"""
        xpath_summary = {
            'investor_rules': result.get('investor_xpath_rules', []),
            'news_rules': result.get('news_xpath_rules', []),
            'general_rules': result.get('general_xpath_rules', []),
            'url_specific_rules': {}
        }

        # 整理URL特定的规则
        if 'url_xpath_mapping' in result:
            url_mapping = result['url_xpath_mapping']
            for url, rules in url_mapping.items():
                if any(rules.get(rule_type, []) for rule_type in ['investor_xpath_rules', 'news_xpath_rules', 'general_xpath_rules']):
                    xpath_summary['url_specific_rules'][url] = {
                        'investor_rules': rules.get('investor_xpath_rules', []),
                        'news_rules': rules.get('news_xpath_rules', []),
                        'general_rules': rules.get('general_xpath_rules', [])
                    }

        return xpath_summary
    
    def get_task_status(self, task_id):
        """获取任务状态"""
        if task_id in self.active_tasks:
            return self.active_tasks[task_id]
        return None
    
    def get_task_result(self, task_id):
        """获取任务结果"""
        if task_id in self.task_results:
            return self.task_results[task_id]
        return None
    
    def cancel_task(self, task_id):
        """取消任务"""
        if task_id in self.active_tasks:
            self.active_tasks[task_id]['status'] = 'cancelled'
            self.active_tasks[task_id]['completed_at'] = datetime.utcnow()
            return True
        return False
    
    def get_user_tasks(self, user_id, limit=50):
        """获取用户的任务列表"""
        user_tasks = []
        for task_id, task_info in self.active_tasks.items():
            if task_info['user_id'] == user_id:
                user_tasks.append(task_info)
        
        # 按创建时间倒序排列
        user_tasks.sort(key=lambda x: x['created_at'], reverse=True)
        return user_tasks[:limit]


# 全局搜索服务实例
search_service = WebSearchService()
