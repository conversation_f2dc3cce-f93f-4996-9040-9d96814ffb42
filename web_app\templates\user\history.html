{% extends "base.html" %}

{% block title %}搜索历史 - AI工具新闻查找平台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-history me-2"></i>搜索历史
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('user.search') }}" class="btn btn-sm btn-primary">
                <i class="fas fa-search me-1"></i>新建搜索
            </a>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshHistory()">
                <i class="fas fa-sync-alt me-1"></i>刷新
            </button>
        </div>
    </div>
</div>

<!-- 搜索过滤器 -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">搜索过滤</h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label for="statusFilter" class="form-label">状态</label>
                <select class="form-select" id="statusFilter" onchange="filterHistory()">
                    <option value="">全部状态</option>
                    <option value="completed">已完成</option>
                    <option value="running">进行中</option>
                    <option value="failed">失败</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="dateFrom" class="form-label">开始日期</label>
                <input type="date" class="form-control" id="dateFrom" onchange="filterHistory()">
            </div>
            <div class="col-md-3">
                <label for="dateTo" class="form-label">结束日期</label>
                <input type="date" class="form-control" id="dateTo" onchange="filterHistory()">
            </div>
            <div class="col-md-3">
                <label for="companySearch" class="form-label">公司名称</label>
                <input type="text" class="form-control" id="companySearch" placeholder="搜索公司..." onkeyup="filterHistory()">
            </div>
        </div>
    </div>
</div>

<!-- 搜索历史列表 -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">历史记录</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="historyTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>任务ID</th>
                        <th>公司名称</th>
                        <th>AI源</th>
                        <th>数据源</th>
                        <th>状态</th>
                        <th>开始时间</th>
                        <th>完成时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="historyTableBody">
                    <!-- 模拟数据 -->
                    <tr>
                        <td>TASK_001</td>
                        <td>苹果公司</td>
                        <td>OpenAI GPT-4</td>
                        <td>Google搜索</td>
                        <td><span class="badge bg-success">已完成</span></td>
                        <td>2025-07-03 09:00:00</td>
                        <td>2025-07-03 09:05:30</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-info" onclick="viewResult('TASK_001')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="downloadResult('TASK_001')">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="retrySearch('TASK_001')">
                                    <i class="fas fa-redo"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>TASK_002</td>
                        <td>腾讯控股</td>
                        <td>OpenAI GPT-4</td>
                        <td>Google搜索</td>
                        <td><span class="badge bg-success">已完成</span></td>
                        <td>2025-07-03 08:30:00</td>
                        <td>2025-07-03 08:36:15</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-info" onclick="viewResult('TASK_002')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="downloadResult('TASK_002')">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="retrySearch('TASK_002')">
                                    <i class="fas fa-redo"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>TASK_003</td>
                        <td>阿里巴巴</td>
                        <td>OpenAI GPT-4</td>
                        <td>Google搜索</td>
                        <td><span class="badge bg-warning">进行中</span></td>
                        <td>2025-07-03 08:00:00</td>
                        <td>-</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-warning" onclick="cancelSearch('TASK_003')">
                                    <i class="fas fa-stop"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="viewProgress('TASK_003')">
                                    <i class="fas fa-chart-line"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>TASK_004</td>
                        <td>Microsoft</td>
                        <td>OpenAI GPT-4</td>
                        <td>Google搜索</td>
                        <td><span class="badge bg-danger">失败</span></td>
                        <td>2025-07-02 16:00:00</td>
                        <td>2025-07-02 16:02:30</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-danger" onclick="viewError('TASK_004')">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="retrySearch('TASK_004')">
                                    <i class="fas fa-redo"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 统计信息 -->
<div class="row">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            总搜索次数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            24
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-search fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            成功完成
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            21
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            进行中
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            1
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-spinner fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            失败
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            2
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 结果查看模态框 -->
<div class="modal fade" id="resultModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">搜索结果详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="resultModalContent">
                <!-- 结果内容将通过JavaScript填充 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="downloadCurrentResult()">
                    <i class="fas fa-download me-1"></i>下载结果
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function filterHistory() {
    const status = $('#statusFilter').val();
    const dateFrom = $('#dateFrom').val();
    const dateTo = $('#dateTo').val();
    const company = $('#companySearch').val().toLowerCase();

    $('#historyTableBody tr').each(function() {
        const row = $(this);
        let show = true;

        // 状态过滤
        if (status) {
            const rowStatus = row.find('.badge').text().trim();
            const statusMap = {
                'completed': '已完成',
                'running': '进行中',
                'failed': '失败'
            };
            if (rowStatus !== statusMap[status]) {
                show = false;
            }
        }

        // 公司名称过滤
        if (company) {
            const rowCompany = row.find('td:eq(1)').text().toLowerCase();
            if (!rowCompany.includes(company)) {
                show = false;
            }
        }

        // 日期过滤（这里简化处理，实际应该解析日期）
        // TODO: 实现日期范围过滤

        row.toggle(show);
    });
}

function viewResult(taskId) {
    // 模拟获取搜索结果
    const mockResult = {
        task_id: taskId,
        company_name: "苹果公司",
        official_website: "https://www.apple.com",
        confidence: 0.95,
        investor_relations_urls: [
            "https://investor.apple.com",
            "https://www.apple.com/investor"
        ],
        news_section_urls: [
            "https://www.apple.com/newsroom",
            "https://www.apple.com/news"
        ],
        xpath_rules: [
            "//div[@class='news-list']//a[@href]",
            "//article//h2/a",
            "//div[@id='press-releases']//a"
        ],
        analysis_summary: {
            total_sections_found: 3,
            investor_sections: 2,
            news_sections: 2,
            xpath_rules_generated: 3
        },
        execution_time: "5分30秒",
        created_at: "2025-07-03 09:00:00"
    };

    $('#resultModalContent').html(`
        <div class="row mb-3">
            <div class="col-md-6">
                <h6>任务信息</h6>
                <table class="table table-sm">
                    <tr><td><strong>任务ID:</strong></td><td>${mockResult.task_id}</td></tr>
                    <tr><td><strong>公司名称:</strong></td><td>${mockResult.company_name}</td></tr>
                    <tr><td><strong>执行时间:</strong></td><td>${mockResult.execution_time}</td></tr>
                    <tr><td><strong>创建时间:</strong></td><td>${mockResult.created_at}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>结果统计</h6>
                <table class="table table-sm">
                    <tr><td><strong>官方网站:</strong></td><td><a href="${mockResult.official_website}" target="_blank">${mockResult.official_website}</a></td></tr>
                    <tr><td><strong>置信度:</strong></td><td><span class="badge bg-success">${(mockResult.confidence * 100).toFixed(1)}%</span></td></tr>
                    <tr><td><strong>发现板块:</strong></td><td>${mockResult.analysis_summary.total_sections_found}</td></tr>
                    <tr><td><strong>XPath规则:</strong></td><td>${mockResult.analysis_summary.xpath_rules_generated}</td></tr>
                </table>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-md-6">
                <h6>投资者关系页面</h6>
                <ul class="list-group list-group-flush">
                    ${mockResult.investor_relations_urls.map(url =>
                        `<li class="list-group-item"><a href="${url}" target="_blank">${url}</a></li>`
                    ).join('')}
                </ul>
            </div>
            <div class="col-md-6">
                <h6>新闻板块页面</h6>
                <ul class="list-group list-group-flush">
                    ${mockResult.news_section_urls.map(url =>
                        `<li class="list-group-item"><a href="${url}" target="_blank">${url}</a></li>`
                    ).join('')}
                </ul>
            </div>
        </div>

        <h6>XPath规则</h6>
        <div class="bg-light p-3 rounded">
            <code>
                ${mockResult.xpath_rules.map(rule => `<div>${rule}</div>`).join('')}
            </code>
        </div>
    `);

    $('#resultModal').modal('show');
}

function downloadResult(taskId) {
    showAlert(`下载任务 ${taskId} 的结果功能待实现`, 'info');
}

function downloadCurrentResult() {
    showAlert('下载当前结果功能待实现', 'info');
}

function retrySearch(taskId) {
    if (confirm(`确定要重新执行任务 ${taskId} 吗？`)) {
        showAlert(`重新执行任务 ${taskId} 功能待实现`, 'info');
    }
}

function cancelSearch(taskId) {
    if (confirm(`确定要取消任务 ${taskId} 吗？`)) {
        showAlert(`取消任务 ${taskId} 功能待实现`, 'warning');
    }
}

function viewProgress(taskId) {
    showAlert(`查看任务 ${taskId} 进度功能待实现`, 'info');
}

function viewError(taskId) {
    showAlert(`查看任务 ${taskId} 错误详情功能待实现`, 'warning');
}

function refreshHistory() {
    location.reload();
}

$(document).ready(function() {
    console.log('搜索历史页面已加载');

    // 设置默认日期范围（最近7天）
    const today = new Date();
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(weekAgo.toISOString().split('T')[0]);
});
</script>
{% endblock %}
