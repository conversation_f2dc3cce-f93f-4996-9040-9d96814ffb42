# AI工具新闻查找Web平台使用指南

## 项目概述

基于现有AI工具新闻查找项目开发的完整Web平台，提供用户友好的界面来管理AI源、数据源，并执行公司调研搜索任务。

## 功能特性

### 用户权限管理
- **两级用户权限**：管理员和普通用户
- **测试账号**：
  - 管理员：用户名 `admin`，密码 `admin`
  - 普通用户：用户名 `user`，密码 `user`
- **快速登录**：支持一键登录测试账号

### 管理员功能
1. **AI源管理**
   - 新增、编辑、删除AI源配置
   - 支持OpenAI GPT系列模型
   - 配置API密钥、端点URL、模型参数
   - 启用/禁用状态管理
   - 连接测试功能

2. **数据源管理**
   - 支持多种数据源类型：Google搜索、MySQL、Elasticsearch
   - 灵活的配置参数管理
   - 连接测试和状态监控

3. **用户管理**
   - 查看所有用户信息
   - 用户状态管理
   - 登录历史追踪

4. **系统监控**
   - 实时系统日志
   - 统计信息仪表板
   - 系统健康状态监控

### 普通用户功能
1. **搜索任务**
   - 选择AI源和数据源
   - 输入公司名称进行调研
   - 高级搜索选项配置
   - 实时搜索进度监控

2. **实时日志系统**
   - WebSocket实时日志推送
   - 分级日志显示（信息、警告、错误等）
   - 搜索节点状态追踪

3. **结果管理**
   - 结构化结果展示
   - 支持JSON格式导出
   - 搜索历史记录
   - 结果数据可视化

## 技术架构

### 后端技术栈
- **框架**：Flask 2.3+
- **数据库**：SQLite（开发）/ MySQL（生产）
- **认证**：Flask-Login
- **实时通信**：Flask-SocketIO
- **ORM**：Flask-SQLAlchemy

### 前端技术栈
- **UI框架**：Bootstrap 5
- **JavaScript库**：jQuery 3.6
- **实时通信**：Socket.IO
- **图标**：Font Awesome 6

### 核心集成
- **搜索引擎**：集成现有的SearchResearchClass
- **AI模型**：支持OpenAI GPT系列
- **网页抓取**：DrissionPage备用方案
- **数据处理**：智能内容清理和结构化

## 安装部署

### 环境要求
- Python 3.8+
- pip包管理器
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 安装步骤

1. **安装依赖**
```bash
pip install flask flask-login flask-socketio flask-sqlalchemy werkzeug
```

2. **初始化数据库**
```bash
python database/init_db.py init
```

3. **启动Web应用**
```bash
python web_app/app.py
```

4. **访问平台**
- 打开浏览器访问：http://localhost:5000
- 使用测试账号登录

### 配置说明

#### 环境变量配置
```bash
# Flask配置
FLASK_SECRET_KEY=your-secret-key
FLASK_DEBUG=True
FLASK_CONFIG=development

# 数据库配置
DATABASE_URL=sqlite:///ai_tools_web.db

# 日志配置
WEB_LOG_LEVEL=INFO
WEB_LOG_FILE=logs/web_app.log
```

#### AI源配置
在管理员界面中配置AI源：
- **名称**：AI源显示名称
- **端点URL**：API服务地址
- **API密钥**：访问凭证
- **模型名称**：使用的模型标识
- **配置参数**：超时、Token限制等

#### 数据源配置
支持的数据源类型：

1. **Google搜索**
   - API URL：搜索服务地址
   - 最大结果数：返回结果限制
   - 超时时间：请求超时设置

2. **MySQL数据库**
   - 主机地址、端口
   - 数据库名、用户名、密码

3. **Elasticsearch**
   - 主机地址、端口
   - 索引名、认证信息

## 使用指南

### 管理员操作流程

1. **登录系统**
   - 使用admin/admin登录
   - 或点击"管理员登录"快速登录

2. **配置AI源**
   - 进入"AI源管理"
   - 点击"添加AI源"
   - 填写配置信息并测试连接
   - 启用AI源

3. **配置数据源**
   - 进入"数据源管理"
   - 选择数据源类型
   - 配置连接参数
   - 测试连接并启用

4. **监控系统**
   - 查看仪表板统计信息
   - 监控系统日志
   - 管理用户账号

### 普通用户操作流程

1. **登录系统**
   - 使用user/user登录
   - 或点击"普通用户登录"快速登录

2. **执行搜索**
   - 进入"搜索任务"页面
   - 选择AI源和数据源
   - 输入公司名称
   - 配置高级选项（可选）
   - 点击"开始搜索"

3. **监控进度**
   - 实时查看搜索日志
   - 监控任务进度
   - 等待搜索完成

4. **查看结果**
   - 浏览结构化搜索结果
   - 查看投资者关系页面
   - 查看新闻板块信息
   - 导出JSON格式结果

5. **历史管理**
   - 查看搜索历史
   - 重新执行搜索
   - 下载历史结果

## 故障排除

### 常见问题

1. **无法启动Web应用**
   - 检查Python版本和依赖安装
   - 确认端口5000未被占用
   - 查看错误日志

2. **数据库连接失败**
   - 运行数据库初始化命令
   - 检查数据库文件权限
   - 确认SQLite支持

3. **AI源连接失败**
   - 验证API密钥正确性
   - 检查网络连接
   - 确认端点URL可访问

4. **搜索任务失败**
   - 检查AI源和数据源配置
   - 查看实时日志错误信息
   - 确认现有搜索模块正常

### 日志查看
- Web应用日志：`logs/web_app.log`
- 系统日志：管理员界面 -> 系统日志
- 浏览器控制台：F12开发者工具

## 扩展开发

### 添加新的AI源类型
1. 扩展AISource模型
2. 更新配置参数结构
3. 实现连接测试逻辑
4. 更新前端配置界面

### 添加新的数据源类型
1. 在DataSource模型中添加新类型
2. 实现连接测试方法
3. 更新搜索服务集成
4. 添加前端配置选项

### 自定义搜索流程
1. 修改SearchService类
2. 扩展WebSocket日志节点
3. 更新前端进度显示
4. 添加新的结果格式

## 安全注意事项

1. **生产环境部署**
   - 更改默认密钥和密码
   - 使用HTTPS协议
   - 配置防火墙规则
   - 定期备份数据库

2. **API密钥管理**
   - 安全存储敏感信息
   - 定期轮换密钥
   - 限制访问权限

3. **用户权限控制**
   - 定期审查用户权限
   - 监控异常登录
   - 实施密码策略

## 技术支持

如有问题或建议，请联系开发团队或查看项目文档。

---

**版本**：v1.0.0  
**更新时间**：2025-07-03  
**开发团队**：AI工具新闻查找项目组
