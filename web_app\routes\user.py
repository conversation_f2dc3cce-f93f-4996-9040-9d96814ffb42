"""
普通用户路由
"""
from flask import Blueprint, render_template, request, jsonify
from flask_login import current_user
from web_app.models.ai_source import AISource
from web_app.models.data_source import DataSource
from web_app.utils.decorators import login_required
from web_app.services.search_service import search_service

user_bp = Blueprint('user', __name__, url_prefix='/user')


@user_bp.route('/dashboard')
@login_required
def dashboard():
    """用户仪表板"""
    # 获取可用的AI源和数据源
    ai_sources = AISource.get_enabled_sources()
    data_sources = DataSource.get_enabled_sources()
    
    return render_template('user/dashboard.html', 
                         ai_sources=ai_sources,
                         data_sources=data_sources)


@user_bp.route('/search')
@login_required
def search():
    """搜索任务页面"""
    # 获取可用的AI源和数据源
    ai_sources = AISource.get_enabled_sources()
    data_sources = DataSource.get_enabled_sources()
    
    return render_template('user/search.html', 
                         ai_sources=ai_sources,
                         data_sources=data_sources)


@user_bp.route('/history')
@login_required
def history():
    """搜索历史页面"""
    return render_template('user/history.html')


@user_bp.route('/api/available-sources')
@login_required
def api_available_sources():
    """获取可用的AI源和数据源API"""
    ai_sources = AISource.get_enabled_sources()
    data_sources = DataSource.get_enabled_sources()
    
    return jsonify({
        'success': True,
        'data': {
            'ai_sources': [source.to_dict() for source in ai_sources],
            'data_sources': [source.to_dict() for source in data_sources]
        }
    })


@user_bp.route('/api/search', methods=['POST'])
@login_required
def api_search():
    """执行搜索任务API"""
    if not request.is_json:
        return jsonify({'success': False, 'message': '需要JSON格式'}), 400

    data = request.get_json()

    # 验证必要字段
    required_fields = ['ai_source_id', 'data_source_id', 'company_name']
    for field in required_fields:
        if not data.get(field):
            return jsonify({
                'success': False,
                'message': f'缺少必要字段: {field}'
            }), 400

    try:
        # 提取搜索选项
        options = {
            'search_depth': data.get('search_depth', 'standard'),
            'result_format': data.get('result_format', 'json'),
            'include_news': data.get('include_news', True),
            'enable_fallback': data.get('enable_fallback', True)
        }

        # 启动搜索任务
        success, message, task_id = search_service.start_search_task(
            user_id=current_user.id,
            ai_source_id=data['ai_source_id'],
            data_source_id=data['data_source_id'],
            company_name=data['company_name'],
            options=options
        )

        if success:
            # 获取任务信息
            task_info = search_service.get_task_status(task_id)
            return jsonify({
                'success': True,
                'message': message,
                'data': {
                    'task_id': task_id,
                    'status': task_info['status'],
                    'ai_source': task_info['ai_source'],
                    'data_source': task_info['data_source'],
                    'company_name': task_info['company_name'],
                    'created_at': task_info['created_at'].isoformat()
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'搜索启动失败: {str(e)}'
        }), 500


@user_bp.route('/api/search/<task_id>/status')
@login_required
def api_search_status(task_id):
    """获取搜索任务状态API"""
    try:
        task_info = search_service.get_task_status(task_id)
        if not task_info:
            return jsonify({
                'success': False,
                'message': '任务不存在'
            }), 404

        # 检查用户权限
        if task_info['user_id'] != current_user.id:
            return jsonify({
                'success': False,
                'message': '无权访问此任务'
            }), 403

        return jsonify({
            'success': True,
            'data': {
                'task_id': task_id,
                'status': task_info['status'],
                'created_at': task_info['created_at'].isoformat(),
                'started_at': task_info['started_at'].isoformat() if task_info['started_at'] else None,
                'completed_at': task_info['completed_at'].isoformat() if task_info['completed_at'] else None,
                'error': task_info.get('error')
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取任务状态失败: {str(e)}'
        }), 500


@user_bp.route('/api/search/<task_id>/result')
@login_required
def api_search_result(task_id):
    """获取搜索任务结果API"""
    try:
        task_info = search_service.get_task_status(task_id)
        if not task_info:
            return jsonify({
                'success': False,
                'message': '任务不存在'
            }), 404

        # 检查用户权限
        if task_info['user_id'] != current_user.id:
            return jsonify({
                'success': False,
                'message': '无权访问此任务'
            }), 403

        # 检查任务是否完成
        if task_info['status'] != 'completed':
            return jsonify({
                'success': False,
                'message': '任务尚未完成'
            }), 400

        result = search_service.get_task_result(task_id)
        return jsonify({
            'success': True,
            'data': result
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取任务结果失败: {str(e)}'
        }), 500


@user_bp.route('/api/search/<task_id>/cancel', methods=['POST'])
@login_required
def api_cancel_search(task_id):
    """取消搜索任务API"""
    try:
        task_info = search_service.get_task_status(task_id)
        if not task_info:
            return jsonify({
                'success': False,
                'message': '任务不存在'
            }), 404

        # 检查用户权限
        if task_info['user_id'] != current_user.id:
            return jsonify({
                'success': False,
                'message': '无权操作此任务'
            }), 403

        # 取消任务
        success = search_service.cancel_task(task_id)
        if success:
            return jsonify({
                'success': True,
                'message': '任务已取消'
            })
        else:
            return jsonify({
                'success': False,
                'message': '取消任务失败'
            }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'取消任务失败: {str(e)}'
        }), 500


@user_bp.route('/api/search/history')
@login_required
def api_search_history():
    """获取用户搜索历史API"""
    try:
        limit = request.args.get('limit', 50, type=int)
        tasks = search_service.get_user_tasks(current_user.id, limit)

        # 格式化任务数据
        formatted_tasks = []
        for task in tasks:
            formatted_task = {
                'task_id': task['task_id'],
                'company_name': task['company_name'],
                'ai_source': task['ai_source']['name'],
                'data_source': task['data_source']['name'],
                'status': task['status'],
                'created_at': task['created_at'].isoformat(),
                'started_at': task['started_at'].isoformat() if task['started_at'] else None,
                'completed_at': task['completed_at'].isoformat() if task['completed_at'] else None
            }
            formatted_tasks.append(formatted_task)

        return jsonify({
            'success': True,
            'data': formatted_tasks
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取搜索历史失败: {str(e)}'
        }), 500


@user_bp.route('/api/search/<task_id>/export/<format>')
@login_required
def api_export_result(task_id, format):
    """导出搜索结果API"""
    try:
        task_info = search_service.get_task_status(task_id)
        if not task_info:
            return jsonify({
                'success': False,
                'message': '任务不存在'
            }), 404

        # 检查用户权限
        if task_info['user_id'] != current_user.id:
            return jsonify({
                'success': False,
                'message': '无权访问此任务'
            }), 403

        # 检查任务是否完成
        if task_info['status'] != 'completed':
            return jsonify({
                'success': False,
                'message': '任务尚未完成'
            }), 400

        result = search_service.get_task_result(task_id)
        if not result:
            return jsonify({
                'success': False,
                'message': '无搜索结果可导出'
            }), 404

        if format.lower() == 'json':
            from flask import Response
            import json

            response_data = json.dumps(result, ensure_ascii=False, indent=2)
            response = Response(
                response_data,
                mimetype='application/json',
                headers={
                    'Content-Disposition': f'attachment; filename=search_result_{task_id}.json'
                }
            )
            return response

        elif format.lower() == 'excel':
            # 这里可以实现Excel导出功能
            return jsonify({
                'success': False,
                'message': 'Excel导出功能待实现'
            }), 501

        else:
            return jsonify({
                'success': False,
                'message': '不支持的导出格式'
            }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'导出失败: {str(e)}'
        }), 500
