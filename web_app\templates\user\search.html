{% extends "base.html" %}

{% block title %}搜索任务 - AI工具新闻查找平台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-search me-2"></i>搜索任务
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('user.history') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-history me-1"></i>搜索历史
            </a>
        </div>
    </div>
</div>

<!-- 搜索配置 -->
<div class="row">
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-cogs me-2"></i>搜索配置
                </h6>
            </div>
            <div class="card-body">
                <form id="searchForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="aiSource" class="form-label">选择AI源 *</label>
                                <select class="form-select" id="aiSource" name="ai_source_id" required>
                                    <option value="">请选择AI源</option>
                                    {% for source in ai_sources %}
                                    <option value="{{ source.id }}" data-model="{{ source.model_name }}">
                                        {{ source.name }} ({{ source.model_name }})
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">选择用于分析的AI模型</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="dataSource" class="form-label">选择数据源 *</label>
                                <select class="form-select" id="dataSource" name="data_source_id" required>
                                    <option value="">请选择数据源</option>
                                    {% for source in data_sources %}
                                    <option value="{{ source.id }}" data-type="{{ source.source_type }}">
                                        {{ source.name }} ({{ source.source_type }})
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">选择搜索数据的来源</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="companyName" class="form-label">公司名称 *</label>
                        <input type="text" class="form-control" id="companyName" name="company_name" 
                               placeholder="请输入要调研的公司名称，如：苹果公司、腾讯控股" required>
                        <div class="form-text">支持中英文公司名称</div>
                    </div>
                    
                    <!-- 高级选项 -->
                    <div class="mb-3">
                        <button class="btn btn-link p-0" type="button" data-bs-toggle="collapse" 
                                data-bs-target="#advancedOptions" aria-expanded="false">
                            <i class="fas fa-chevron-down me-1"></i>高级选项
                        </button>
                    </div>
                    
                    <div class="collapse" id="advancedOptions">
                        <div class="card card-body bg-light">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="searchDepth" class="form-label">搜索深度</label>
                                        <select class="form-select" id="searchDepth" name="search_depth">
                                            <option value="basic">基础搜索</option>
                                            <option value="standard" selected>标准搜索</option>
                                            <option value="deep">深度搜索</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="resultFormat" class="form-label">结果格式</label>
                                        <select class="form-select" id="resultFormat" name="result_format">
                                            <option value="json" selected>JSON格式</option>
                                            <option value="excel">Excel表格</option>
                                            <option value="pdf">PDF报告</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="includeNews" name="include_news" checked>
                                    <label class="form-check-label" for="includeNews">
                                        包含新闻板块分析
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableFallback" name="enable_fallback" checked>
                                    <label class="form-check-label" for="enableFallback">
                                        启用DrissionPage备用方案
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="button" class="btn btn-outline-secondary me-md-2" onclick="resetForm()">
                            <i class="fas fa-undo me-1"></i>重置
                        </button>
                        <button type="submit" class="btn btn-primary" id="startSearchBtn">
                            <i class="fas fa-search me-1"></i>开始搜索
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 搜索状态 -->
    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-2"></i>搜索状态
                </h6>
            </div>
            <div class="card-body">
                <div id="searchStatus" class="text-center">
                    <i class="fas fa-clock fa-2x text-muted mb-3"></i>
                    <p class="text-muted">等待开始搜索...</p>
                </div>
                
                <!-- 进度条 -->
                <div id="progressSection" style="display: none;">
                    <div class="mb-2">
                        <small class="text-muted">搜索进度</small>
                    </div>
                    <div class="progress mb-3">
                        <div class="progress-bar" role="progressbar" style="width: 0%" id="progressBar">
                            0%
                        </div>
                    </div>
                </div>
                
                <!-- 当前任务信息 -->
                <div id="taskInfo" style="display: none;">
                    <hr>
                    <div class="small">
                        <p><strong>任务ID:</strong> <span id="taskId">-</span></p>
                        <p><strong>公司名称:</strong> <span id="taskCompany">-</span></p>
                        <p><strong>AI源:</strong> <span id="taskAI">-</span></p>
                        <p><strong>数据源:</strong> <span id="taskDataSource">-</span></p>
                        <p><strong>开始时间:</strong> <span id="taskStartTime">-</span></p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 快速示例 -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-lightbulb me-2"></i>快速示例
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <p><strong>示例公司名称：</strong></p>
                    <ul class="list-unstyled">
                        <li><a href="#" onclick="fillExample('苹果公司')">苹果公司</a></li>
                        <li><a href="#" onclick="fillExample('腾讯控股')">腾讯控股</a></li>
                        <li><a href="#" onclick="fillExample('阿里巴巴')">阿里巴巴</a></li>
                        <li><a href="#" onclick="fillExample('Microsoft')">Microsoft</a></li>
                        <li><a href="#" onclick="fillExample('Google')">Google</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 实时日志 -->
<div class="card shadow mb-4" id="logSection" style="display: none;">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-terminal me-2"></i>实时日志
        </h6>
        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearLogs()">
            <i class="fas fa-trash me-1"></i>清空
        </button>
    </div>
    <div class="card-body">
        <div id="logContainer" class="log-container">
            <!-- 日志内容将通过JavaScript动态添加 -->
        </div>
    </div>
</div>

<!-- 搜索结果 -->
<div class="card shadow mb-4" id="resultSection" style="display: none;">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-chart-bar me-2"></i>搜索结果
        </h6>
        <div class="btn-group btn-group-sm">
            <button type="button" class="btn btn-outline-primary" onclick="exportResult('json')">
                <i class="fas fa-download me-1"></i>导出JSON
            </button>
            <button type="button" class="btn btn-outline-success" onclick="exportResult('excel')">
                <i class="fas fa-file-excel me-1"></i>导出Excel
            </button>
        </div>
    </div>
    <div class="card-body">
        <div id="resultContent">
            <!-- 搜索结果将通过JavaScript动态显示 -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentTaskId = null;
let searchInterval = null;
let socket = null;

function fillExample(companyName) {
    $('#companyName').val(companyName);

    // 自动选择第一个可用的AI源和数据源
    if ($('#aiSource option').length > 1) {
        $('#aiSource').val($('#aiSource option:eq(1)').val());
    }
    if ($('#dataSource option').length > 1) {
        $('#dataSource').val($('#dataSource option:eq(1)').val());
    }
}

function resetForm() {
    $('#searchForm')[0].reset();
    $('#searchStatus').html(`
        <i class="fas fa-clock fa-2x text-muted mb-3"></i>
        <p class="text-muted">等待开始搜索...</p>
    `);
    $('#progressSection').hide();
    $('#taskInfo').hide();
    $('#logSection').hide();
    $('#resultSection').hide();

    if (searchInterval) {
        clearInterval(searchInterval);
        searchInterval = null;
    }
}

function updateProgress(percentage, status) {
    $('#progressBar').css('width', percentage + '%').text(percentage + '%');

    if (percentage < 30) {
        $('#progressBar').removeClass().addClass('progress-bar bg-info');
    } else if (percentage < 70) {
        $('#progressBar').removeClass().addClass('progress-bar bg-warning');
    } else {
        $('#progressBar').removeClass().addClass('progress-bar bg-success');
    }
}

function addLogEntry(level, message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `
        <div class="log-entry log-${level}">
            <span class="log-timestamp">[${timestamp}]</span>
            <span class="log-level">[${level.toUpperCase()}]</span>
            <span class="log-message">${message}</span>
        </div>
    `;

    $('#logContainer').append(logEntry);

    // 自动滚动到底部
    const container = document.getElementById('logContainer');
    container.scrollTop = container.scrollHeight;
}

function clearLogs() {
    $('#logContainer').empty();
}

function initializeWebSocket() {
    if (socket) {
        socket.disconnect();
    }

    socket = io();

    socket.on('connect', function() {
        console.log('WebSocket连接成功');
    });

    socket.on('disconnect', function() {
        console.log('WebSocket连接断开');
    });

    socket.on('log_added', function(data) {
        if (data.task_id === currentTaskId) {
            addLogEntry(data.log.level, data.log.message);
        }
    });

    socket.on('progress_updated', function(data) {
        if (data.task_id === currentTaskId) {
            updateProgress(data.progress, '进度更新');
        }
    });

    socket.on('node_started', function(data) {
        if (data.task_id === currentTaskId) {
            $('#searchStatus').html(`
                <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
                <p class="text-primary">${data.node.name}</p>
            `);
        }
    });

    socket.on('node_completed', function(data) {
        if (data.task_id === currentTaskId) {
            if (data.node_id === 'root') {
                // 任务完成
                $('#searchStatus').html(`
                    <i class="fas fa-check-circle fa-2x text-success mb-3"></i>
                    <p class="text-success">搜索完成！</p>
                `);

                // 获取搜索结果
                fetchSearchResult(currentTaskId);

                // 重新启用搜索按钮
                $('#startSearchBtn').prop('disabled', false).html('<i class="fas fa-search me-1"></i>开始搜索');
            }
        }
    });

    socket.on('node_failed', function(data) {
        if (data.task_id === currentTaskId) {
            $('#searchStatus').html(`
                <i class="fas fa-times-circle fa-2x text-danger mb-3"></i>
                <p class="text-danger">搜索失败: ${data.error}</p>
            `);

            // 重新启用搜索按钮
            $('#startSearchBtn').prop('disabled', false).html('<i class="fas fa-search me-1"></i>开始搜索');
        }
    });
}

function startRealTimeSearch(taskData) {
    // 显示任务信息
    $('#taskId').text(taskData.task_id);
    $('#taskCompany').text(taskData.company_name);
    $('#taskAI').text(taskData.ai_source.name);
    $('#taskDataSource').text(taskData.data_source.name);
    $('#taskStartTime').text(new Date().toLocaleString());

    $('#taskInfo').show();
    $('#progressSection').show();
    $('#logSection').show();

    // 加入任务房间以接收实时更新
    if (socket) {
        socket.emit('join_task', { task_id: taskData.task_id });
    }

    $('#searchStatus').html(`
        <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
        <p class="text-primary">正在启动搜索...</p>
    `);
}

function fetchSearchResult(taskId) {
    $.ajax({
        url: `/user/api/search/${taskId}/result`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                showSearchResult({ task_id: taskId }, response.data);
            } else {
                addLogEntry('error', response.message);
            }
        },
        error: function() {
            addLogEntry('error', '获取搜索结果失败');
        }
    });
}

function showSearchResult(taskData, result = null) {
    // 使用真实的搜索结果数据，如果没有则显示错误
    if (!result) {
        $('#resultContent').html(`
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                暂无搜索结果数据
            </div>
        `);
        $('#resultSection').show();
        return;
    }

    const searchResult = result;

    // 安全地获取数据，避免undefined和NaN
    const companyName = searchResult.company_name || '未知公司';
    const officialWebsite = searchResult.official_website || searchResult.base_url || '未找到官网';
    const confidence = searchResult.confidence || 0;
    const confidencePercent = isNaN(confidence) ? 0 : (confidence * 100).toFixed(1);

    // 获取分析统计数据
    const stats = searchResult.analysis_summary || {};
    const totalSections = stats.total_sections_found || 0;
    const investorSections = stats.investor_sections || 0;
    const newsSections = stats.news_sections || 0;
    const mixedSections = stats.mixed_sections || 0;
    const xpathRulesCount = stats.xpath_rules_generated || 0;
    const uniqueUrls = stats.unique_urls || 0;

    // 获取URL列表
    const investorUrls = searchResult.investor_relations_urls || [];
    const newsUrls = searchResult.news_section_urls || [];
    const mixedUrls = searchResult.mixed_section_urls || [];

    // 获取XPath规则
    const xpathSummary = searchResult.xpath_rules_summary || {};
    const investorRules = xpathSummary.investor_rules || [];
    const newsRules = xpathSummary.news_rules || [];
    const generalRules = xpathSummary.general_rules || [];
    const urlSpecificRules = xpathSummary.url_specific_rules || {};

    // 置信度颜色
    let confidenceClass = 'bg-secondary';
    if (confidence >= 0.8) confidenceClass = 'bg-success';
    else if (confidence >= 0.6) confidenceClass = 'bg-warning';
    else if (confidence >= 0.3) confidenceClass = 'bg-info';
    else confidenceClass = 'bg-danger';

    $('#resultContent').html(`
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-info-circle me-2"></i>基本信息</h6>
                <table class="table table-sm">
                    <tr><td><strong>公司名称:</strong></td><td>${companyName}</td></tr>
                    <tr><td><strong>官方网站:</strong></td><td>
                        ${officialWebsite !== '未找到官网' ?
                            `<a href="${officialWebsite}" target="_blank">${officialWebsite}</a>` :
                            '<span class="text-muted">未找到官网</span>'
                        }
                    </td></tr>
                    <tr><td><strong>置信度:</strong></td><td><span class="badge ${confidenceClass}">${confidencePercent}%</span></td></tr>
                    <tr><td><strong>研究时间:</strong></td><td>${searchResult.research_timestamp ? new Date(searchResult.research_timestamp * 1000).toLocaleString() : '未知'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-chart-bar me-2"></i>分析统计</h6>
                <table class="table table-sm">
                    <tr><td><strong>发现板块数:</strong></td><td><span class="badge bg-primary">${totalSections}</span></td></tr>
                    <tr><td><strong>投资者关系:</strong></td><td><span class="badge bg-info">${investorSections}</span></td></tr>
                    <tr><td><strong>新闻板块:</strong></td><td><span class="badge bg-warning">${newsSections}</span></td></tr>
                    <tr><td><strong>混合板块:</strong></td><td><span class="badge bg-secondary">${mixedSections}</span></td></tr>
                    <tr><td><strong>XPath规则:</strong></td><td><span class="badge bg-success">${xpathRulesCount}</span></td></tr>
                    <tr><td><strong>唯一URL:</strong></td><td><span class="badge bg-dark">${uniqueUrls}</span></td></tr>
                </table>
            </div>
        </div>

        <hr>

        <div class="row">
            <div class="col-md-4">
                <h6><i class="fas fa-building me-2"></i>投资者关系页面 (${investorUrls.length})</h6>
                <div class="list-group list-group-flush" style="max-height: 300px; overflow-y: auto;">
                    ${investorUrls.length > 0 ?
                        investorUrls.map(url =>
                            `<a href="${url}" target="_blank" class="list-group-item list-group-item-action">
                                <i class="fas fa-external-link-alt me-2"></i>${url}
                            </a>`
                        ).join('') :
                        '<div class="list-group-item text-muted">暂无投资者关系页面</div>'
                    }
                </div>
            </div>
            <div class="col-md-4">
                <h6><i class="fas fa-newspaper me-2"></i>新闻板块页面 (${newsUrls.length})</h6>
                <div class="list-group list-group-flush" style="max-height: 300px; overflow-y: auto;">
                    ${newsUrls.length > 0 ?
                        newsUrls.map(url =>
                            `<a href="${url}" target="_blank" class="list-group-item list-group-item-action">
                                <i class="fas fa-external-link-alt me-2"></i>${url}
                            </a>`
                        ).join('') :
                        '<div class="list-group-item text-muted">暂无新闻板块页面</div>'
                    }
                </div>
            </div>
            <div class="col-md-4">
                <h6><i class="fas fa-layer-group me-2"></i>混合板块页面 (${mixedUrls.length})</h6>
                <div class="list-group list-group-flush" style="max-height: 300px; overflow-y: auto;">
                    ${mixedUrls.length > 0 ?
                        mixedUrls.map(url =>
                            `<a href="${url}" target="_blank" class="list-group-item list-group-item-action">
                                <i class="fas fa-external-link-alt me-2"></i>${url}
                            </a>`
                        ).join('') :
                        '<div class="list-group-item text-muted">暂无混合板块页面</div>'
                    }
                </div>
            </div>
        </div>

        <hr>

        <h6><i class="fas fa-code me-2"></i>XPath规则分析 - URL特定规则</h6>
        ${Object.keys(urlSpecificRules).length > 0 ? `
        <div class="accordion" id="xpathAccordion">
            ${Object.entries(urlSpecificRules).map(([url, rules], index) => {
                const totalRules = (rules.investor_rules?.length || 0) + (rules.news_rules?.length || 0) + (rules.general_rules?.length || 0);
                const isExpanded = index === 0 ? 'show' : '';
                const buttonExpanded = index === 0 ? 'true' : 'false';
                const buttonCollapsed = index === 0 ? '' : 'collapsed';

                return `
                <div class="accordion-item">
                    <h2 class="accordion-header" id="urlRulesHeader${index}">
                        <button class="accordion-button ${buttonCollapsed}" type="button" data-bs-toggle="collapse"
                                data-bs-target="#urlRules${index}" aria-expanded="${buttonExpanded}">
                            <div class="d-flex align-items-center w-100">
                                <i class="fas fa-link me-2"></i>
                                <div class="flex-grow-1">
                                    <div class="fw-bold">${url}</div>
                                    <small class="text-muted">共 ${totalRules} 条规则</small>
                                </div>
                                <div class="ms-2">
                                    ${rules.investor_rules && rules.investor_rules.length > 0 ?
                                        `<span class="badge bg-info me-1">投资者 ${rules.investor_rules.length}</span>` : ''}
                                    ${rules.news_rules && rules.news_rules.length > 0 ?
                                        `<span class="badge bg-warning me-1">新闻 ${rules.news_rules.length}</span>` : ''}
                                    ${rules.general_rules && rules.general_rules.length > 0 ?
                                        `<span class="badge bg-secondary">通用 ${rules.general_rules.length}</span>` : ''}
                                </div>
                            </div>
                        </button>
                    </h2>
                    <div id="urlRules${index}" class="accordion-collapse collapse ${isExpanded}" data-bs-parent="#xpathAccordion">
                        <div class="accordion-body">
                            <div class="mb-2">
                                <a href="${url}" target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-external-link-alt me-1"></i>访问页面
                                </a>
                            </div>

                            ${rules.investor_rules && rules.investor_rules.length > 0 ? `
                            <div class="mb-3">
                                <h6 class="text-info"><i class="fas fa-building me-2"></i>投资者关系规则 (${rules.investor_rules.length})</h6>
                                <div class="bg-light p-3 rounded" style="max-height: 200px; overflow-y: auto;">
                                    <code class="small">
                                        ${rules.investor_rules.map(rule => `<div class="mb-1 p-1 border-bottom">${rule}</div>`).join('')}
                                    </code>
                                </div>
                            </div>` : ''}

                            ${rules.news_rules && rules.news_rules.length > 0 ? `
                            <div class="mb-3">
                                <h6 class="text-warning"><i class="fas fa-newspaper me-2"></i>新闻板块规则 (${rules.news_rules.length})</h6>
                                <div class="bg-light p-3 rounded" style="max-height: 200px; overflow-y: auto;">
                                    <code class="small">
                                        ${rules.news_rules.map(rule => `<div class="mb-1 p-1 border-bottom">${rule}</div>`).join('')}
                                    </code>
                                </div>
                            </div>` : ''}

                            ${rules.general_rules && rules.general_rules.length > 0 ? `
                            <div class="mb-3">
                                <h6 class="text-secondary"><i class="fas fa-cogs me-2"></i>通用规则 (${rules.general_rules.length})</h6>
                                <div class="bg-light p-3 rounded" style="max-height: 200px; overflow-y: auto;">
                                    <code class="small">
                                        ${rules.general_rules.map(rule => `<div class="mb-1 p-1 border-bottom">${rule}</div>`).join('')}
                                    </code>
                                </div>
                            </div>` : ''}
                        </div>
                    </div>
                </div>`;
            }).join('')}
        </div>` :
        '<div class="alert alert-info"><i class="fas fa-info-circle me-2"></i>暂无URL特定的XPath规则</div>'}
    `);

    $('#resultSection').show();
}

function exportResult(format) {
    if (!currentTaskId) {
        showAlert('没有可导出的搜索结果', 'warning');
        return;
    }

    // 创建下载链接
    const downloadUrl = `/user/api/search/${currentTaskId}/export/${format}`;

    // 创建临时下载链接
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `search_result_${currentTaskId}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showAlert(`正在下载${format.toUpperCase()}格式的搜索结果`, 'success');
}

$(document).ready(function() {
    // 初始化WebSocket连接
    initializeWebSocket();

    // 搜索表单提交
    $('#searchForm').on('submit', function(e) {
        e.preventDefault();

        const formData = {
            ai_source_id: $('#aiSource').val(),
            data_source_id: $('#dataSource').val(),
            company_name: $('#companyName').val().trim(),
            search_depth: $('#searchDepth').val(),
            result_format: $('#resultFormat').val(),
            include_news: $('#includeNews').is(':checked'),
            enable_fallback: $('#enableFallback').is(':checked')
        };

        // 验证表单
        if (!formData.ai_source_id || !formData.data_source_id || !formData.company_name) {
            showAlert('请填写所有必要字段', 'danger');
            return;
        }

        // 禁用搜索按钮
        $('#startSearchBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>启动中...');

        // 清空之前的结果
        $('#logContainer').empty();
        $('#resultSection').hide();

        // 发送搜索请求
        $.ajax({
            url: '/user/api/search',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                if (response.success) {
                    currentTaskId = response.data.task_id;
                    addLogEntry('success', response.message);

                    // 开始实时搜索过程
                    startRealTimeSearch(response.data);
                } else {
                    showAlert(response.message, 'danger');
                    $('#startSearchBtn').prop('disabled', false).html('<i class="fas fa-search me-1"></i>开始搜索');
                }
            },
            error: function(xhr) {
                let message = '搜索启动失败';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                showAlert(message, 'danger');
                $('#startSearchBtn').prop('disabled', false).html('<i class="fas fa-search me-1"></i>开始搜索');
            }
        });
    });

    console.log('搜索页面已加载');
});
</script>
{% endblock %}
